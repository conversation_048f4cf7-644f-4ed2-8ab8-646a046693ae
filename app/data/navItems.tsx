import AIIcon from "@/app/icons/AIIcon.svg";
import DigitalTransformationIcon from "@/app/icons/DigitalTransformationIcon.svg";
import ExcellenceIcon from "@/app/icons/ExcellenceIcon.svg";
import InfrastructureIcon from "@/app/icons/InfrastructureIcon.svg";
import SatelliteIcon from "@/app/icons/SatelliteIcon.svg";
import CenterFocus from "@/app/icons/CenterFocus.svg";
import City from "@/app/icons/City.svg";
import CheckmarkBadge from "@/app/icons/CheckmarkBadge.svg";
import Question from "@/app/icons/Question.svg";
import MoneyRecive from "@/app/icons/MoneyRecive.svg";
import Video from "@/app/icons/Video.svg";
import GiveStar from "@/app/icons/GiveStar.svg";
import NewOffice from "@/app/icons/NewOffice.svg";
import RealEstate from "@/app/icons/RealEstate.svg";
import EditRoad from "@/app/icons/EditRoad.svg";
import LaptopPerformance from "@/app/icons/LaptopPreformence.svg";

export const navigationItems = [
  {
    title: "home",
    href: "/",
  },
  {
    title: "gtWorld",
    href: "/gt-world",
  },
  {
    title: "gt-services",
    isMegaMenu: true,
    items: [
      {
        title: "digitalTransformation",
        description: "digitalTransformationDesc",
        icon: (
          <DigitalTransformationIcon
            style={{ width: "36px", height: "36px" }}
          />
        ),
        href: "/gt-services/digital-transformation",
        items: [
          {
            title: "imageAnalysis",
            description: "defaultDescription",
            href: "/gt-services/digital-transformation/image-analysis",
            icon: <SatelliteIcon style={{ width: "24px", height: "24px" }} />,
          },
          {
            title: "urbanGrowthMonitoring",
            description: "defaultDescription",
            href: "/gt-services/digital-transformation/urban-growth-monitoring",
            icon: <CenterFocus style={{ width: "24px", height: "24px" }} />,
          },
          {
            title: "decisionSupport",
            description: "defaultDescription",
            href: "/gt-services/digital-transformation/decision-support",
            icon: <City style={{ width: "24px", height: "24px" }} />,
          },
        ],
      },
      {
        title: "aiDataAnalysis",
        description: "aiDataAnalysisDesc",
        icon: <AIIcon style={{ width: "36px", height: "36px" }} />,
        href: "/gt-services/ai-data-analysis",
        items: [
          {
            title: "streamlining",
            description: "defaultDescription",
            href: "/gt-services/ai-data-analysis/streamlining",
            icon: <CheckmarkBadge style={{ width: "24px", height: "24px" }} />,
          },
          {
            title: "platforms",
            description: "defaultDescription",
            href: "/gt-services/ai-data-analysis/platforms",
            icon: <Question style={{ width: "24px", height: "24px" }} />,
          },
          {
            title: "digitalTwin",
            description: "defaultDescription",
            href: "/gt-services/ai-data-analysis/digital-twin",
            icon: <MoneyRecive style={{ width: "24px", height: "24px" }} />,
          },
        ],
      },
      {
        title: "operationalExcellence",
        description: "operationalExcellenceDesc",
        icon: <ExcellenceIcon style={{ width: "36px", height: "36px" }} />,
        href: "/gt-services/operational-excellence",
        items: [
          {
            title: "monitoringSystems",
            description: "defaultDescription",
            href: "/gt-services/operational-excellence/monitoring-systems",
            icon: <Video style={{ width: "24px", height: "24px" }} />,
          },
          {
            title: "qualityControl",
            description: "defaultDescription",
            href: "/gt-services/operational-excellence/quality-control",
            icon: <GiveStar style={{ width: "24px", height: "24px" }} />,
          },
          {
            title: "facilityManagement",
            description: "defaultDescription",
            href: "/gt-services/operational-excellence/facility-management",
            icon: <NewOffice style={{ width: "24px", height: "24px" }} />,
          },
        ],
      },
      {
        title: "smartInfrastructure",
        description: "smartInfrastructureDesc",
        icon: <InfrastructureIcon style={{ width: "36px", height: "36px" }} />,
        href: "/gt-services/smart-infrastructure",
        items: [
          {
            title: "propertySolutions",
            description: "defaultDescription",
            href: "/gt-services/smart-infrastructure/property-solutions",
            icon: <RealEstate style={{ width: "24px", height: "24px" }} />,
          },
          {
            title: "planningStudies",
            description: "defaultDescription",
            href: "/gt-services/smart-infrastructure/planning-studies",
            icon: <EditRoad style={{ width: "24px", height: "24px" }} />,
          },
          {
            title: "integratedSystems",
            description: "defaultDescription",
            href: "/gt-services/smart-infrastructure/integrated-systems",
            icon: (
              <LaptopPerformance style={{ width: "24px", height: "24px" }} />
            ),
          },
        ],
      },
    ],
  },
  {
    title: "gtVision",
    href: "/gt-vision",
  },
  {
    title: "contactUs",
    href: "/contact-us",
  },
];

export const services = {
  title: "services",
  isMegaMenu: true,
  items: [
    {
      title: "digitalTransformation",
      description: "digitalTransformationDesc",
      icon: (
        <DigitalTransformationIcon style={{ width: "36px", height: "36px" }} />
      ),
      href: "/gt-services/digital-transformation",
      items: [
        {
          title: "imageAnalysis",
          description: "defaultDescription",
          href: "/gt-services/digital-transformation/image-analysis",
          icon: <SatelliteIcon style={{ width: "24px", height: "24px" }} />,
        },
        {
          title: "urbanGrowthMonitoring",
          description: "defaultDescription",
          href: "/gt-services/digital-transformation/urban-growth-monitoring",
          icon: <CenterFocus style={{ width: "24px", height: "24px" }} />,
        },
        {
          title: "decisionSupport",
          description: "defaultDescription",
          href: "/gt-services/digital-transformation/decision-support",
          icon: <City style={{ width: "24px", height: "24px" }} />,
        },
      ],
    },
    {
      title: "aiDataAnalysis",
      description: "aiDataAnalysisDesc",
      icon: <AIIcon style={{ width: "36px", height: "36px" }} />,
      href: "/gt-services/ai-data-analysis",
      items: [
        {
          title: "streamlining",
          description: "defaultDescription",
          href: "/gt-services/ai-data-analysis/streamlining",
          icon: <CheckmarkBadge style={{ width: "24px", height: "24px" }} />,
        },
        {
          title: "platforms",
          description: "defaultDescription",
          href: "/gt-services/ai-data-analysis/platforms",
          icon: <Question style={{ width: "24px", height: "24px" }} />,
        },
        {
          title: "digitalTwin",
          description: "defaultDescription",
          href: "/gt-services/ai-data-analysis/digital-twin",
          icon: <MoneyRecive style={{ width: "24px", height: "24px" }} />,
        },
      ],
    },
    {
      title: "operationalExcellence",
      description: "operationalExcellenceDesc",
      icon: <ExcellenceIcon style={{ width: "36px", height: "36px" }} />,
      href: "/gt-services/operational-excellence",
      items: [
        {
          title: "monitoringSystems",
          description: "defaultDescription",
          href: "/gt-services/operational-excellence/monitoring-systems",
          icon: <Video style={{ width: "24px", height: "24px" }} />,
        },
        {
          title: "qualityControl",
          description: "defaultDescription",
          href: "/gt-services/operational-excellence/quality-control",
          icon: <GiveStar style={{ width: "24px", height: "24px" }} />,
        },
        {
          title: "facilityManagement",
          description: "defaultDescription",
          href: "/gt-services/operational-excellence/facility-management",
          icon: <NewOffice style={{ width: "24px", height: "24px" }} />,
        },
      ],
    },
    {
      title: "smartInfrastructure",
      description: "smartInfrastructureDesc",
      icon: <InfrastructureIcon style={{ width: "36px", height: "36px" }} />,
      href: "/gt-services/smart-infrastructure",
      items: [
        {
          title: "propertySolutions",
          description: "defaultDescription",
          href: "/gt-services/smart-infrastructure/property-solutions",
          icon: <RealEstate style={{ width: "24px", height: "24px" }} />,
        },
        {
          title: "planningStudies",
          description: "defaultDescription",
          href: "/gt-services/smart-infrastructure/planning-studies",
          icon: <EditRoad style={{ width: "24px", height: "24px" }} />,
        },
        {
          title: "integratedSystems",
          description: "defaultDescription",
          href: "/gt-services/smart-infrastructure/integrated-systems",
          icon: <LaptopPerformance style={{ width: "24px", height: "24px" }} />,
        },
      ],
    },
  ],
};
