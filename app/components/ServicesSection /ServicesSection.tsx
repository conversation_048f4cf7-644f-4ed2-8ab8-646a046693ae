"use client";

import type React from "react";

import { useState } from "react";
import Link from "next/link";
import { <PERSON>ton } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowUpRight } from "lucide-react";
import { services } from "@/app/data/navItems";
import { useTranslations, useLocale } from "next-intl";
import { SectionHeader } from "@/components/SectionHeader/SectionHeader";
import { Section } from "@/components/Section/Section";

export default function ServicesSection() {
  const [hoveredService, setHoveredService] = useState<string | null>(null);
  const t = useTranslations("ServicesSection");
  const locale = useLocale();

  return (
    <Section variant="white" padding="default" relative>
      <div className="container mx-auto px-4 relative z-10">
        <SectionHeader
          badge={{
            text: t("badge"),
            variant: "purple",
            className: "border border-[#C5B7FF]",
          }}
          title={t("heading")}
          subtitle={t("description")}
          align="center"
        />

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4  mb-12 justify-center gap-6 mt-16">
          {services.items.map((service, index) => (
            <Card
              key={index}
              className={` group relative w-[300px] h-[250px] transition-all duration-300 cursor-pointer shadow-none border-2  ${
                hoveredService === service.title
                  ? "border-[#5840BA] shadow-[0px_0px_9px_0px_#5840BABF] bg-white"
                  : "border-[#ECECEC] hover:border-[#5840BA] bg-white"
              }`}
              onMouseEnter={() => setHoveredService(service.title)}
              onMouseLeave={() => setHoveredService(null)}
            >
              <div
                className={`absolute inset-0 z-0 bg-no-repeat transition-all duration-300
                          ${
                            locale === "en"
                              ? 'bg-[url("/ServicesMask.svg")] md:bg-[url("/ServicesMaskBW.svg")] group-hover:bg-[url("/ServicesMask.svg")]  bg-corner-small bg-rotated'
                              : 'bg-[url("/ServicesMask.svg")] md:bg-[url("/ServicesMaskBW.svg")] group-hover:bg-[url("/ServicesMask.svg")]  bg-corner-small'
                          }
                        `}
              />
              <CardContent
                className={`h-full flex flex-col transition-all duration-300 ${
                  hoveredService === service.title
                    ? "pt-4"
                    : "pt-4 sm:pt-6 lg:pt-8"
                }`}
              >
                {/* Icon */}
                <div className="mb-[8px]">
                  <div
                    className={`transition-colors w-[36px] h-[36px]   duration-300 ${
                      hoveredService === service.title
                        ? "text-[#5840BA]"
                        : " text-[#5840BA] md:text-[#6D6D6D] "
                    }`}
                  >
                    {service.icon}
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-[#424242] mb-[12px] leading-tight hover:text-[#000]">
                    {t(service.title)}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {t(service.description)}
                  </p>
                </div>

                <div
                  className={`transition-opacity duration-300 ${
                    hoveredService === service.title
                      ? "opacity-100 inline-flex items-center text-[#5840BA] hover:text-[#5840BA] font-medium group/link text-sm"
                      : "lg:opacity-0 lg:invisible opacity-100 text-[#5840BA] text-sm flex mt-2"
                  }`}
                >
                  <Link
                    href={service.href}
                    className="flex underline items-center"
                  >
                    <span>
                      {t("moreAbout")}{" "}
                      {t(service.title).split(" ").slice(0, 2).join(" ")}
                    </span>
                    <ArrowUpRight className="w-4 h-4 mr-2" />
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Explore All Services Button */}
        <div className="text-center">
          <Link href="/gt-services">
            <Button
              size="lg"
              className="bg-[#5840BA] rounded-full text-white px-8 py-4 text-base shadow-none"
            >
              <span className="ml-[12px]  text-base font-medium text-white">
                {t("exploreAll")}
              </span>
              <ArrowUpRight className="w-[20px] h-[20px]" />
            </Button>
          </Link>
        </div>
      </div>
    </Section>
  );
}
