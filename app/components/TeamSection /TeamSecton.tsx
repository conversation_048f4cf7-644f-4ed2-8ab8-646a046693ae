"use client";

import { Card, CardContent } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { SectionHeader } from "@/components/SectionHeader/SectionHeader";
import { Section } from "@/components/Section/Section";

export default function TeamSection() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const t = useTranslations("TeamSection");
  const teamMembers = t.raw("members");
  return (
    <Section variant="white" padding="default" relative>
      <div className="container mx-auto px-4 relative z-10">
        <SectionHeader
          badge={{
            text: t("badge"),
            variant: "purple",
            className: "border border-[#C5B7FF]",
          }}
          title={t("heading")}
          subtitle={t("description")}
          align="center"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-16 max-w-[900px] mx-auto">
          {teamMembers.map((member, index) => (
            <Card
              key={index}
              className={`bg-[url(/ServicesMask.svg)] md:bg-[url(/ServicesMaskBW.svg)] md:hover:bg-[url(/ServicesMask.svg)] bg-no-repeat bg-[length:70px] top-0 left-0 group relative transition-all duration-300 cursor-pointer shadow-none mx-auto w-full max-w-[240px] h-[137px] ${
                hoveredIndex === member.title
                  ? "border-2 border-[#5840BA] shadow-[0px_0px_9px_0px_#5840BABF] bg-white transform scale-105"
                  : "border-2 border-[#ECECEC] hover:border-4 hover:border-[#5840BA] bg-white"
              }`}
              onMouseEnter={() => setHoveredIndex(member.title)}
              onMouseLeave={() => setHoveredIndex(null)}
            >
              <CardContent className="flex flex-col justify-center items-center px-0">
                {/* Icon */}
                <div
                  className={`transition-colors text-[10px] px-2  py-1 rounded-full duration-300 mb-2 ${
                    hoveredIndex === member.title
                      ? "bg-[#5840BA1A] text-[#5840BA]"
                      : "lg:bg-[#ADADAD1A] lg:text-[#949494] bg-[#5840BA1A] text-[#5840BA]"
                  }`}
                >
                  <div className="font-semibold">{member.title}</div>
                </div>

                {/* Content */}
                <div className="flex-1 items-center flex flex-col text-center gap-2">
                  <h3 className="text-[18px] font-bold text-black  leading-tight">
                    {member.name}
                  </h3>
                  <p className="text-[#424242] text-xs leading-relaxed ">
                    {member.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </Section>
  );
}
