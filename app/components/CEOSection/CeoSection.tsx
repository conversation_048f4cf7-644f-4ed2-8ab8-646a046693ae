import { SectionHeader } from "@/components/SectionHeader/SectionHeader";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Section } from "@/components/Section/Section";
import PalmIcon from "@/app/icons/PalmIcon.svg";
import { useTranslations } from "next-intl";
import { Quote } from "lucide-react";

export default function CEOSection() {
  const t = useTranslations("CEOSection");

  return (
    <Section variant="white" padding="default">
      <SectionHeader
        badge={{
          text: t("badge"),
          variant: "green",
          icon: <PalmIcon style={{ width: "22px", height: "22px" }} />,
          className:"border border-[#1B835433]",
        }}
        title={t("heading")}
        subtitle={t("description")}
        align="center"
      />

      {/* Quote Icon */}
      <div className="my-5 flex justify-center">
        <Quote
          className="w-10 h-6 md:w-12 md:h-7 text-[#1B835440] mx-auto rotate-180"
          fill="#1B835440"
          strokeWidth={0}
          arabicForm="medial"
        />
      </div>

      {/* CEO Profile */}
      <div className="flex flex-col items-center">
        <Avatar className="w-20 h-20 md:w-24 md:h-24 mb-4 grayscale">
          <AvatarImage src="CEOImage.png" alt={t("ceoName")} />
        </Avatar>
        <div className="text-base md:text-lg font-semibold text-gray-900">
          {t("ceoName")}
        </div>
        <div className="text-xs md:text-sm text-[#1B8354]">{t("ceoTitle")}</div>
      </div>
    </Section>
  );
}
