"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { ArrowUpRight } from "lucide-react";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { SectionHeader } from "@/components/SectionHeader/SectionHeader";
import { Section } from "@/components/Section/Section";

interface StatItem {
  number: number;
  suffix?: string;
  titleKey: string;
}

const stats: StatItem[] = [
  {
    number: 7,
    suffix: "+",
    titleKey: "yearsOfExperience",
  },
  {
    number: 50,
    suffix: "+",
    titleKey: "expertsAndSpecialists",
  },
  {
    number: 15,
    suffix: "+",
    titleKey: "leadingProjects",
  },
];

function AnimatedCounter({
  target,
  suffix = "",
  duration = 2000,
}: {
  target: number;
  suffix?: string;
  duration?: number;
}) {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  useEffect(() => {
    if (!isVisible) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);

      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      setCount(Math.floor(target * easeOutQuart));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationFrame);
  }, [isVisible, target, duration]);

  return (
    <div
      ref={ref}
      className="text-5xl md:text-6xl font-bold text-[#D4B63A] mb-4"
    >
      {count}
      {suffix === "+" ? "+" : ""}
    </div>
  );
}

export default function NumbersSection() {
  const t = useTranslations("NumbersSection");
  return (
    <Section variant="white" padding="default" relative>
      {/* Background Pattern */}
      <div className="bg-[url(/Pattern.svg)] w-full absolute inset-0 left-0 bg-no-repeat bg-center bg-cover " />

      <div className="container mx-auto px-4 relative z-10 ">
        <SectionHeader
          badge={{
            text: t("badge"),
            variant: "purple",
            className: "border border-[#C5B7FF]",
          }}
          title={t("heading")}
          subtitle={t("description")}
          align="center"
        />

        <div className="md:hidden sm:block mt-16 ">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 mb-16">
            {/* Mobile: first stat centered, next two side by side; Desktop: 3 columns */}
            <div className="flex flex-col items-center justify-center md:order-none order-1">
              <div className="mb-4">
                <span className="inline-block bg-[#FBF8ED] text-[#D4B63A] px-7 py-2 rounded-full text-base font-bold">
                  {t(stats[2].titleKey)}
                </span>
              </div>
              <AnimatedCounter
                target={stats[2].number}
                suffix={stats[2].suffix}
                duration={2600}
              />
            </div>
            <div className="flex flex-row items-center justify-center gap-8 md:flex- md:gap-0 md:order-none order-2">
              <div className="flex flex-col items-center justify-center flex-1">
                <div className="mb-4">
                  <span className="inline-block bg-[#FBF8ED] text-[#D4B63A] px-7 py-2 rounded-full text-base font-bold">
                    {t(stats[0].titleKey)}
                  </span>
                </div>
                <AnimatedCounter
                  target={stats[0].number}
                  suffix={stats[0].suffix}
                  duration={2000}
                />
              </div>
              <div className="flex flex-col items-center justify-center flex-1">
                <div className="mb-4">
                  <span className="inline-block bg-[#FBF8ED] text-[#D4B63A] px-7 py-2 rounded-full text-base font-bold">
                    {t(stats[1].titleKey)}
                  </span>
                </div>
                <AnimatedCounter
                  target={stats[1].number}
                  suffix={stats[1].suffix}
                  duration={2300}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="hidden md:block mt-16">
          {/* Stats */}
          <div className="grid md:grid-cols-3 px-20 md:px-10 lg:px-50 xl:px-70 mb-16">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                {/* Title */}
                <div className="mb-2">
                  <span className="inline-block bg-[#FBF8ED] text-[#D4B63A] px-10 py-2 rounded-full text-sm font-medium">
                    {t(stat.titleKey)}
                  </span>
                </div>

                {/* Number */}
                <AnimatedCounter
                  target={stat.number}
                  suffix={stat.suffix}
                  duration={2000 + index * 300}
                />
              </div>
            ))}
          </div>
        </div>
        {/* Stats */}

        {/* CTA Button */}
        <div className="text-center my-4 hidden md:block ">
          <Link href="/about">
            <Button
              size="lg"
              className="bg-[#5840BA] rounded-full text-white px-8 py-4 text-base shadow-none"
            >
              <span className="ml-[12px]  text-base font-medium text-white">
                {t("exploreMore")}
              </span>
              <ArrowUpRight className="w-5 h-5" />
            </Button>
          </Link>
        </div>
      </div>
    </Section>
  );
}
