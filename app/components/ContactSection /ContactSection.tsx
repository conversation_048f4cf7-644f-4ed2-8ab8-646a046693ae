import { Button } from "@/components/ui/button";
import { ArrowUpRight } from "lucide-react";
import { useTranslations, useLocale } from "next-intl";

export default function ContactSection() {
  const t = useTranslations("ContactSection");
  const locale = useLocale();

  return (
    <section className="py-4 md:py-5 relative overflow-hidden">
      <div className="absolute inset-0 bg-[#4730A4]" />
      <div className={`bg-[url(/contactFlowMobile.svg)] md:bg-[url(/contactFlow.svg)] w-full absolute inset-0 left-0 bg-no-repeat bg-center bg-cover mix-blend-luminosity ${
        locale === 'en' ? 'rotate-0' : 'rotate-180'
      }`} />
      <div className="relative z-10 container mx-auto">
        <div className="w-full px-4  text-start text-white mx-auto ">
          <h2 className="text-2xl uppercase sm:text-xl md:text-[28px] font-bold mb-4 md:mb-6">
            {t("heading")}
          </h2>
          <p className="text-[12px] md:text-sm mb-6 md:mb-8 text-white/90 leading-relaxed">
            {t("description")}
          </p>
          <Button
            size="lg"
            className="bg-white text-[#5840BA] rounded-3xl hover:bg-gray-100 px-[8px] md:px-[16px] py-[8px] md:py-[8px] text-sm md:text-[15px] font-semibold"
          >
            {t("cta")}
            <ArrowUpRight className="w-6 h-6 md:w-7 md:h-7 ml-2" />
          </Button>
        </div>
      </div>
    </section>
  );
}
