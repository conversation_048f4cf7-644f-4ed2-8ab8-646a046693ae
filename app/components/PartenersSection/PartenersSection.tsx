import Image from "next/image";
import { useTranslations } from "next-intl";
import { SectionHeader } from "@/components/SectionHeader/SectionHeader";
import { Section } from "@/components/Section/Section";

export default function PartnersSection() {
  const t = useTranslations("PartnersSection");
  const partnerLogos = [
    {
      name: "الهيئة العامة للمساحة",
      logo: "/parteners1.png",
      alt: "General Authority for Survey",
    },
    {
      name: "بنك الرياض",
      logo: "/parteners2.png",
      alt: "Riyad Bank",
    },
    {
      name: "الهيئة العامة للمساحة",
      logo: "/parteners3.png",
      alt: "General Authority for Survey",
    },
    {
      name: "بنك الرياض",
      logo: "/parteners4.png",
      alt: "Riyad Bank",
    },
    {
      name: "وزارة السياحة",
      logo: "/parteners5.png",
      alt: "Ministry of Tourism",
    },
    {
      name: "الهيئة العامة للمساحة",
      logo: "/parteners6.png",
      alt: "General Authority for Survey",
    },
    {
      name: "وزارة الشؤون البلدية والقروية والإسكان",
      logo: "/parteners7.png",
      alt: "Ministry of Municipal and Rural Affairs and Housing",
    },

    {
      name: "الهيئة العامة للمساحة",
      logo: "/parteners8.png",
      alt: "General Authority for Survey",
    },
    {
      name: "وزارة السياحة",
      logo: "/parteners9.png",
      alt: "Ministry of Tourism",
    },
    {
      name: "وزارة الشؤون البلدية والقروية والإسكان",
      logo: "/parteners10.png",
      alt: "Ministry of Municipal and Rural Affairs and Housing",
    },
    {
      name: "بنك الرياض",
      logo: "/parteners11.png",
      alt: "Riyad Bank",
    },
    {
      name: "بنك الرياض",
      logo: "/parteners8.png",
      alt: "Riyad Bank",
    },
    {
      name: "بنك الرياض",
      logo: "/parteners5.png",
      alt: "Riyad Bank",
    },
  ];

  return (
    <Section variant="white" relative overflow>
      <SectionHeader
        badge={{
          text: t("badge"),
          variant: "purple",
          className: "border border-[#C5B7FF]",
        }}
        title={t("heading")}
        subtitle={t("description")}
        align="center"
      />

      <div className="space-y-12 mt-16">
        <div className="flex flex-wrap justify-center items-center gap-4 md:gap-6 lg:gap-8">
          {partnerLogos.map((partner, index) => (
            <div key={index} className="flex items-center justify-center p-2  ">
              <Image
                src={partner.logo || "/placeholder.svg"}
                alt={partner.alt}
                width={150}
                height={50}
              />
            </div>
          ))}
        </div>
      </div>
    </Section>
  );
}
