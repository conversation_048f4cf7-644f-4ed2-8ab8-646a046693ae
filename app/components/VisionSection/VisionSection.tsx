"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, ArrowUpRight } from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import Clock from "@/app/icons/clock.svg";
import { SectionHeader } from "@/components/SectionHeader/SectionHeader";
import { Section } from "@/components/Section/Section";

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  date: string;
  image: string;
  href: string;
}

export default function InsightsSection() {
  const t = useTranslations("VisionSection");
  const blogPosts: BlogPost[] = [
    {
      id: "1",
      title: t("blog1.title"),
      excerpt: t("blog1.excerpt"),
      date: t("blog1.date"),
      image: "/Blog1.svg",
      href: "/gt-vision/saudi-tech-export-agreement",
    },
    {
      id: "2",
      title: t("blog2.title"),
      excerpt: t("blog2.excerpt"),
      date: t("blog2.date"),
      image: "/Blog2.svg",
      href: "/gt-vision/madmoun-company-opening",
    },
    {
      id: "3",
      title: t("blog3.title"),
      excerpt: t("blog3.excerpt"),
      date: t("blog3.date"),
      image: "/Blog3.svg",
      href: "/gt-vision/geotech-real-estate-partnership",
    },
  ];

  return (
    <Section variant="white" padding="default" relative>
      <div className="container mx-auto px-4 relative z-10">
        <SectionHeader
          badge={{
            text: t("badge"),
            variant: "purple",
            className: "border border-[#C5B7FF]",
          }}
          title={t("heading")}
          subtitle={t("description")}
          align="center"
        />

        {/* Blog Posts Grid */}
        <div className="grid lg:grid-cols-2 gap-8 max-w-8xl mx-auto my-12">
          {/* Right Column - One large post */}
          <div>
            {blogPosts.slice(2, 3).map((post) => (
              <Link
                key={post.id}
                href={post.href}
                className="group block h-full"
              >
                <div
                  className={`bg-white border border-[#E3E6EA] rounded-2xl rounded-t-3xl overflow-hidden duration-300 h-full`}
                >
                  {/* Image */}
                  <div className="relative w-full h-48 sm:h-56 md:h-64 lg:h-80 overflow-hidden">
                    <Image
                      src={post.image}
                      alt={post.title}
                      className="w-full h-full rounded-tl-3xl object-cover"
                      width={600}
                      height={400}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 50vw"
                    />
                  </div>

                  {/* Content */}
                  <div className="p-6 flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 leading-tight  transition-colors">
                      {post.title}
                    </h3>
                    <div className="flex items-center text-gray-500 text-sm mb-3">
                      <Clock className="w-4 h-4 ml-2" />
                      <span>{post.date}</span>
                    </div>

                    <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-2 md:line-clamp-3">
                      {post.excerpt}
                    </p>

                    <div className="inline-flex items-center text-[#5840BA]  font-medium transition-colors group/link text-sm">
                      <span className="ml-2">{t("readMore")}</span>
                      <ArrowLeft className="w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {/* Left Column - Two smaller posts */}
          <div className="flex flex-col space-y-9">
            {blogPosts.slice(0, 2).map((post) => (
              <Link
                key={post.id}
                href={post.href}
                className="group block md:h-full "
              >
                <div
                  className={`bg-white border border-[#E3E6EA]  rounded-2xl overflow-hidden duration-300 lg:h-full flex flex-col lg:flex-row-reverse items-center  `}
                >
                  {/* Image */}
                  <div className="relative w-full rounded-t-3xl md:rounded-l-3xl md:rounded-tr-none lg:w-1/2 h-48 sm:h-56 md:h-40 lg:h-full lg:min-h-[200px] overflow-hidden">
                    <Image
                      src={post.image}
                      alt={post.title}
                      className="w-full h-full   object-cover"
                      width={400}
                      height={300}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw"
                    />
                  </div>

                  {/* Content */}
                  <div className="p-6 flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 leading-tight  transition-colors">
                      {post.title}
                    </h3>
                    <div className="flex items-center text-gray-500 text-sm mb-3">
                      <Clock className="w-4 h-4 ml-2" />
                      <span>{post.date}</span>
                    </div>

                    <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-2 md:line-clamp-3">
                      {post.excerpt}
                    </p>

                    <div className="inline-flex items-center text-[#5840BA]  font-medium transition-colors group/link text-sm">
                      <span className="ml-2">{t("readMore")}</span>
                      <ArrowLeft className="w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Explore All News Button */}
        <div className="text-center">
          <Link href="/insights">
            <Button
              size="lg"
              className="bg-[#5840BA] rounded-full text-white px-8 py-4 text-base shadow-none"
            >
              <span className="ml-[12px]  text-base font-medium text-white">
                {t("exploreAllNews")}
              </span>
              <ArrowUpRight className="w-5 h-5" />
            </Button>
          </Link>
        </div>
      </div>
    </Section>
  );
}
