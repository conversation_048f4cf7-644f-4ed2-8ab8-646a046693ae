import { Button } from "@/components/ui/button";
import { ArrowUpRight } from "lucide-react";
import { useTranslations } from "next-intl";
import PalmIcon from "@/app/icons/YellowPalmIcon.svg";

export default function HeroSection() {
  const t = useTranslations("HeroSection");
  return (
    <section className="relative min-h-screen px-5 flex flex-col justify-end items-end  overflow-hidden ">
      <video
        autoPlay
        muted
        loop
        playsInline
        className="w-full h-full absolute top-0 left-0 object-cover object-center pointer-events-none"
        style={{ minHeight: "100%", minWidth: "100%" }}
      >
        <source src="HeroVideo.mp4" type="video/mp4" />
      </video>
      <div className="absolute inset-0 bg-black/70" />

      <div className="relative z-10 text-white w-full container mx-auto  flex flex-col items-start justify-center h-full">
        <div className="mb-6 flex justify-start">
          <div className="mb-2 flex justify-center">
            <span className="inline-flex gap-1 items-center bg-[#D4B63A26] text-[#D4B63A] px-[12px] md:px-[16px] py-[4px] md:py-[8px] rounded-full text-[12px] md:text-[14px] font-medium border-2 border-[#D4B63A33]">
              <PalmIcon style={{ width: "20px", height: "20px" }} />
              {t("nationalTechPartner")}
            </span>
          </div>
        </div>

        <h1 className="text-xl md:text-4xl  lg:text-5xl font-bold mb-2 md:mb-6 leading-tight">
          {t("mainTitle")}
        </h1>

        <p className="text-base hidden  md:block md:text-lg lg:text-xl text-gray-300 mb-8">
          {t("mainDescription")}
        </p>

        <Button
          size="lg"
          className="bg-[#5840BA] rounded-full text-white px-8 py-4 text-base shadow-none px-8 py-4 mt-6 mb-10 text-base"
        >
          {t("exploreSolutions")} <ArrowUpRight />
        </Button>
      </div>
    </section>
  );
}
