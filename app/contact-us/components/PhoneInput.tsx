"use client";

import type React from "react";

import { useEffect, useRef, useState } from "react";
import { ChevronDown } from "lucide-react";
import { Input } from "@/components/ui/input";

interface Country {
  code: string;
  name: string;
  nameAr: string;
  flag: string;
  dialCode: string;
}

const countries: Country[] = [
  {
    code: "SA",
    name: "Saudi Arabia",
    nameAr: "السعودية",
    flag: "🇸🇦",
    dialCode: "+966",
  },
  {
    code: "AE",
    name: "United Arab Emirates",
    nameAr: "الإمارات",
    flag: "🇦🇪",
    dialCode: "+971",
  },
  { code: "EG", name: "Egypt", nameAr: "مصر", flag: "🇪🇬", dialCode: "+20" },
  {
    code: "JO",
    name: "Jordan",
    nameAr: "الأردن",
    flag: "🇯🇴",
    dialCode: "+962",
  },
  {
    code: "KW",
    name: "Kuwait",
    nameAr: "الكويت",
    flag: "🇰🇼",
    dialCode: "+965",
  },
  { code: "QA", name: "Qatar", nameAr: "قطر", flag: "🇶🇦", dialCode: "+974" },
  {
    code: "BH",
    name: "Bahrain",
    nameAr: "البحرين",
    flag: "🇧🇭",
    dialCode: "+973",
  },
  { code: "OM", name: "Oman", nameAr: "عمان", flag: "🇴🇲", dialCode: "+968" },
  {
    code: "LB",
    name: "Lebanon",
    nameAr: "لبنان",
    flag: "🇱🇧",
    dialCode: "+961",
  },
  { code: "SY", name: "Syria", nameAr: "سوريا", flag: "🇸🇾", dialCode: "+963" },
  { code: "IQ", name: "Iraq", nameAr: "العراق", flag: "🇮🇶", dialCode: "+964" },
  { code: "YE", name: "Yemen", nameAr: "اليمن", flag: "🇾🇪", dialCode: "+967" },
  {
    code: "PS",
    name: "Palestine",
    nameAr: "فلسطين",
    flag: "🇵🇸",
    dialCode: "+970",
  },
  {
    code: "MA",
    name: "Morocco",
    nameAr: "المغرب",
    flag: "🇲🇦",
    dialCode: "+212",
  },
  {
    code: "DZ",
    name: "Algeria",
    nameAr: "الجزائر",
    flag: "🇩🇿",
    dialCode: "+213",
  },
  { code: "TN", name: "Tunisia", nameAr: "تونس", flag: "🇹🇳", dialCode: "+216" },
  { code: "LY", name: "Libya", nameAr: "ليبيا", flag: "🇱🇾", dialCode: "+218" },
  {
    code: "SD",
    name: "Sudan",
    nameAr: "السودان",
    flag: "🇸🇩",
    dialCode: "+249",
  },
  {
    code: "US",
    name: "United States",
    nameAr: "الولايات المتحدة",
    flag: "🇺🇸",
    dialCode: "+1",
  },
  {
    code: "GB",
    name: "United Kingdom",
    nameAr: "المملكة المتحدة",
    flag: "🇬🇧",
    dialCode: "+44",
  },
  { code: "FR", name: "France", nameAr: "فرنسا", flag: "🇫🇷", dialCode: "+33" },
  {
    code: "DE",
    name: "Germany",
    nameAr: "ألمانيا",
    flag: "🇩🇪",
    dialCode: "+49",
  },
  { code: "IT", name: "Italy", nameAr: "إيطاليا", flag: "🇮🇹", dialCode: "+39" },
  { code: "ES", name: "Spain", nameAr: "إسبانيا", flag: "🇪🇸", dialCode: "+34" },
  {
    code: "NL",
    name: "Netherlands",
    nameAr: "هولندا",
    flag: "🇳🇱",
    dialCode: "+31",
  },
  {
    code: "BE",
    name: "Belgium",
    nameAr: "بلجيكا",
    flag: "🇧🇪",
    dialCode: "+32",
  },
  {
    code: "CH",
    name: "Switzerland",
    nameAr: "سويسرا",
    flag: "🇨🇭",
    dialCode: "+41",
  },
  {
    code: "AT",
    name: "Austria",
    nameAr: "النمسا",
    flag: "🇦🇹",
    dialCode: "+43",
  },
  { code: "SE", name: "Sweden", nameAr: "السويد", flag: "🇸🇪", dialCode: "+46" },
  {
    code: "NO",
    name: "Norway",
    nameAr: "النرويج",
    flag: "🇳🇴",
    dialCode: "+47",
  },
  {
    code: "DK",
    name: "Denmark",
    nameAr: "الدنمارك",
    flag: "🇩🇰",
    dialCode: "+45",
  },
  {
    code: "FI",
    name: "Finland",
    nameAr: "فنلندا",
    flag: "🇫🇮",
    dialCode: "+358",
  },
  { code: "PL", name: "Poland", nameAr: "بولندا", flag: "🇵🇱", dialCode: "+48" },
  {
    code: "CZ",
    name: "Czech Republic",
    nameAr: "التشيك",
    flag: "🇨🇿",
    dialCode: "+420",
  },
  { code: "HU", name: "Hungary", nameAr: "المجر", flag: "🇭🇺", dialCode: "+36" },
  {
    code: "RO",
    name: "Romania",
    nameAr: "رومانيا",
    flag: "🇷🇴",
    dialCode: "+40",
  },
  {
    code: "BG",
    name: "Bulgaria",
    nameAr: "بلغاريا",
    flag: "🇧🇬",
    dialCode: "+359",
  },
  {
    code: "GR",
    name: "Greece",
    nameAr: "اليونان",
    flag: "🇬🇷",
    dialCode: "+30",
  },
  { code: "TR", name: "Turkey", nameAr: "تركيا", flag: "🇹🇷", dialCode: "+90" },
  { code: "RU", name: "Russia", nameAr: "روسيا", flag: "🇷🇺", dialCode: "+7" },
  {
    code: "UA",
    name: "Ukraine",
    nameAr: "أوكرانيا",
    flag: "🇺🇦",
    dialCode: "+380",
  },
  {
    code: "BY",
    name: "Belarus",
    nameAr: "بيلاروسيا",
    flag: "🇧🇾",
    dialCode: "+375",
  },
  {
    code: "LT",
    name: "Lithuania",
    nameAr: "ليتوانيا",
    flag: "🇱🇹",
    dialCode: "+370",
  },
  {
    code: "LV",
    name: "Latvia",
    nameAr: "لاتفيا",
    flag: "🇱🇻",
    dialCode: "+371",
  },
  {
    code: "EE",
    name: "Estonia",
    nameAr: "إستونيا",
    flag: "🇪🇪",
    dialCode: "+372",
  },
  { code: "CN", name: "China", nameAr: "الصين", flag: "🇨🇳", dialCode: "+86" },
  { code: "JP", name: "Japan", nameAr: "اليابان", flag: "🇯🇵", dialCode: "+81" },
  {
    code: "KR",
    name: "South Korea",
    nameAr: "كوريا الجنوبية",
    flag: "🇰🇷",
    dialCode: "+82",
  },
  { code: "IN", name: "India", nameAr: "الهند", flag: "🇮🇳", dialCode: "+91" },
  {
    code: "PK",
    name: "Pakistan",
    nameAr: "باكستان",
    flag: "🇵🇰",
    dialCode: "+92",
  },
  {
    code: "BD",
    name: "Bangladesh",
    nameAr: "بنغلاديش",
    flag: "🇧🇩",
    dialCode: "+880",
  },
  {
    code: "LK",
    name: "Sri Lanka",
    nameAr: "سريلانكا",
    flag: "🇱🇰",
    dialCode: "+94",
  },
  {
    code: "TH",
    name: "Thailand",
    nameAr: "تايلاند",
    flag: "🇹🇭",
    dialCode: "+66",
  },
  {
    code: "VN",
    name: "Vietnam",
    nameAr: "فيتنام",
    flag: "🇻🇳",
    dialCode: "+84",
  },
  {
    code: "MY",
    name: "Malaysia",
    nameAr: "ماليزيا",
    flag: "🇲🇾",
    dialCode: "+60",
  },
  {
    code: "SG",
    name: "Singapore",
    nameAr: "سنغافورة",
    flag: "🇸🇬",
    dialCode: "+65",
  },
  {
    code: "ID",
    name: "Indonesia",
    nameAr: "إندونيسيا",
    flag: "🇮🇩",
    dialCode: "+62",
  },
  {
    code: "PH",
    name: "Philippines",
    nameAr: "الفلبين",
    flag: "🇵🇭",
    dialCode: "+63",
  },
  {
    code: "AU",
    name: "Australia",
    nameAr: "أستراليا",
    flag: "🇦🇺",
    dialCode: "+61",
  },
  {
    code: "NZ",
    name: "New Zealand",
    nameAr: "نيوزيلندا",
    flag: "🇳🇿",
    dialCode: "+64",
  },
  { code: "CA", name: "Canada", nameAr: "كندا", flag: "🇨🇦", dialCode: "+1" },
  {
    code: "MX",
    name: "Mexico",
    nameAr: "المكسيك",
    flag: "🇲🇽",
    dialCode: "+52",
  },
  {
    code: "BR",
    name: "Brazil",
    nameAr: "البرازيل",
    flag: "🇧🇷",
    dialCode: "+55",
  },
  {
    code: "AR",
    name: "Argentina",
    nameAr: "الأرجنتين",
    flag: "🇦🇷",
    dialCode: "+54",
  },
  { code: "CL", name: "Chile", nameAr: "تشيلي", flag: "🇨🇱", dialCode: "+56" },
  {
    code: "CO",
    name: "Colombia",
    nameAr: "كولومبيا",
    flag: "🇨🇴",
    dialCode: "+57",
  },
  { code: "PE", name: "Peru", nameAr: "بيرو", flag: "🇵🇪", dialCode: "+51" },
  {
    code: "VE",
    name: "Venezuela",
    nameAr: "فنزويلا",
    flag: "🇻🇪",
    dialCode: "+58",
  },
  {
    code: "ZA",
    name: "South Africa",
    nameAr: "جنوب أفريقيا",
    flag: "🇿🇦",
    dialCode: "+27",
  },
  {
    code: "NG",
    name: "Nigeria",
    nameAr: "نيجيريا",
    flag: "🇳🇬",
    dialCode: "+234",
  },
  { code: "KE", name: "Kenya", nameAr: "كينيا", flag: "🇰🇪", dialCode: "+254" },
  { code: "GH", name: "Ghana", nameAr: "غانا", flag: "🇬🇭", dialCode: "+233" },
  {
    code: "ET",
    name: "Ethiopia",
    nameAr: "إثيوبيا",
    flag: "🇪🇹",
    dialCode: "+251",
  },
  {
    code: "TZ",
    name: "Tanzania",
    nameAr: "تنزانيا",
    flag: "🇹🇿",
    dialCode: "+255",
  },
  {
    code: "UG",
    name: "Uganda",
    nameAr: "أوغندا",
    flag: "🇺🇬",
    dialCode: "+256",
  },
  {
    code: "RW",
    name: "Rwanda",
    nameAr: "رواندا",
    flag: "🇷🇼",
    dialCode: "+250",
  },
  { code: "IR", name: "Iran", nameAr: "إيران", flag: "🇮🇷", dialCode: "+98" },
  {
    code: "AF",
    name: "Afghanistan",
    nameAr: "أفغانستان",
    flag: "🇦🇫",
    dialCode: "+93",
  },
  {
    code: "UZ",
    name: "Uzbekistan",
    nameAr: "أوزبكستان",
    flag: "🇺🇿",
    dialCode: "+998",
  },
  {
    code: "KZ",
    name: "Kazakhstan",
    nameAr: "كازاخستان",
    flag: "🇰🇿",
    dialCode: "+7",
  },
  {
    code: "KG",
    name: "Kyrgyzstan",
    nameAr: "قيرغيزستان",
    flag: "🇰🇬",
    dialCode: "+996",
  },
  {
    code: "TJ",
    name: "Tajikistan",
    nameAr: "طاجيكستان",
    flag: "🇹🇯",
    dialCode: "+992",
  },
  {
    code: "TM",
    name: "Turkmenistan",
    nameAr: "تركمانستان",
    flag: "🇹🇲",
    dialCode: "+993",
  },
  {
    code: "AZ",
    name: "Azerbaijan",
    nameAr: "أذربيجان",
    flag: "🇦🇿",
    dialCode: "+994",
  },
  {
    code: "AM",
    name: "Armenia",
    nameAr: "أرمينيا",
    flag: "🇦🇲",
    dialCode: "+374",
  },
  {
    code: "GE",
    name: "Georgia",
    nameAr: "جورجيا",
    flag: "🇬🇪",
    dialCode: "+995",
  },
];


interface PhoneInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
}

export default function PhoneInput({
  onChange,
  placeholder = "123456789",
}: PhoneInputProps) {
  const [selectedCountry, setSelectedCountry] = useState<Country>(countries[0]);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const filteredCountries = countries.filter(
    (country) =>
      country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      country.nameAr.includes(searchTerm) ||
      country.dialCode.includes(searchTerm)
  );

  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    setIsOpen(false);
    setSearchTerm("");
    inputRef.current?.focus();
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPhoneNumber = e.target.value;
    setPhoneNumber(newPhoneNumber);
    const fullNumber = `${selectedCountry.dialCode}${newPhoneNumber}`;
    onChange?.(fullNumber);
  };

  const handleInputFocus = () => {
    inputRef.current?.focus();
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchTerm("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative w-full" ref={dropdownRef}>
      <div className="relative">
        {/* Main Input Field */}
        <div className="relative flex w-full items-center">
          <Input
            ref={inputRef}
            type="tel"
            placeholder={placeholder}
            value={phoneNumber}
            dir="rtl"
            onChange={handlePhoneChange}
            className="w-full shadow-none bg-[#F9FAFB] border border-[#D1D5DB]"
            onClick={handleInputFocus}
          />

          {/* Country Selector - Positioned inside the input */}
          <div className="absolute left-2 top-1/2 transform -translate-y-1/2 flex items-center">
            <button
              type="button"
              onClick={() => setIsOpen(!isOpen)}
              className="flex items-center space-x-1 space-x-reverse gap-1 px-2 py-1 hover:bg-gray-50 "
            >
              <ChevronDown className="w-3 h-3 text-gray-400" />
              <span className="text-sm text-gray-600 font-medium">
                {selectedCountry.dialCode}
              </span>
              <span className="text-base">{selectedCountry.flag}</span>
            </button>
          </div>
        </div>

        {/* Dropdown */}
        {isOpen && (
          <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-300 rounded-md  max-h-60 overflow-hidden">
            {/* Search */}
            <div className="p-2 border-b">
              <Input
                type="text"
                placeholder="البحث عن دولة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full shadow-none bg-[#F9FAFB] border border-[#D1D5DB]"
              />
            </div>

            {/* Countries List */}
            <div className="max-h-48 overflow-y-auto">
              {filteredCountries.map((country) => (
                <button
                  key={country.code}
                  type="button"
                  onClick={() => handleCountrySelect(country)}
                  className="w-full flex items-center px-3 py-2 text-right hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
                >
                  <span className="text-lg ml-3">{country.flag}</span>
                  <div className="flex-1 text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {country.nameAr}
                    </div>
                    <div className="text-xs text-gray-500">{country.name}</div>
                  </div>
                  <span className="text-sm text-gray-600 mr-3">
                    {country.dialCode}
                  </span>
                </button>
              ))}
              {filteredCountries.length === 0 && (
                <div className="px-3 py-2 text-center text-gray-500 text-sm">
                  لا توجد نتائج
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}