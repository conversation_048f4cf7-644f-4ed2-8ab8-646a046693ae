// components/DirectionWrapper.tsx

"use client";

import { DirectionProvider } from "@radix-ui/react-direction";
import { ReactNode } from "react";

type DirectionWrapperProps = React.ComponentProps<typeof DirectionProvider> & {
  children: ReactNode;
  dir?: "ltr" | "rtl";
};

export default function DirectionWrapper({
  children,
  dir,
  ...props
}: DirectionWrapperProps) {
  return <DirectionProvider dir={dir} {...props}>{children}</DirectionProvider>;
}
