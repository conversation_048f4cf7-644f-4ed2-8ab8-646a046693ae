@import "tailwindcss";

body {
  font-family: var(--font-ibm-plex-arabic), sans-serif;
}

:root {
  --background: #ffffff;
  --foreground: #09090b;
  --card: #ffffff;
  --card-foreground: #09090b;
  --popover: #ffffff;
  --popover-foreground: #09090b;
  --primary: #5840ba;
  --primary-foreground: #ffffff;
  --secondary: #f4f4f5;
  --secondary-foreground: #18181b;
  --muted: #f4f4f5;
  --muted-foreground: #71717b;
  --accent: #f4f4f5;
  --accent-foreground: #18181b;
  --destructive: #e7000b;
  --border: #e4e4e7;
  --input: #e4e4e7;
  --ring: #9f9fa9;
  --chart-1: #f54900;
  --chart-2: #009689;
  --chart-3: #104e64;
  --chart-4: #ffb900;
  --chart-5: #fe9a00;
  --sidebar: #fafafa;
  --sidebar-foreground: #09090b;
  --sidebar-primary: #18181b;
  --sidebar-primary-foreground: #fafafa;
  --sidebar-accent: #f4f4f5;
  --sidebar-accent-foreground: #18181b;
  --sidebar-border: #e4e4e7;
  --sidebar-ring: #9f9fa9;

  --font-sans: "Geist", "Geist Fallback", ui-sans-serif, system-ui,
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue",
    Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: "Geist", "Geist Fallback", ui-serif, Georgia, Cambria,
    "Times New Roman", Times, serif;
  --font-mono: "Geist Mono", "Geist Mono Fallback", ui-monospace, SFMono-Regular,
    Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  --radius: 0.625rem;

  --shadow-2xs: 0 1px 3px 0px #000000;
  --shadow-xs: 0 1px 3px 0px #000000;
  --shadow-sm: 0 1px 3px 0px #000000, 0 1px 2px -1px #000000;
  --shadow: 0 1px 3px 0px #000000, 0 1px 2px -1px #000000;
  --shadow-md: 0 1px 3px 0px #000000, 0 2px 4px -1px #000000;
  --shadow-lg: 0 1px 3px 0px #000000, 0 4px 6px -1px #000000;
  --shadow-xl: 0 1px 3px 0px #000000, 0 8px 10px -1px #000000;
  --shadow-2xl: 0 1px 3px 0px #000000;
}

.dark {
  --background: #09090b;
  --foreground: #fafafa;
  --card: #18181b;
  --card-foreground: #fafafa;
  --popover: #18181b;
  --popover-foreground: #fafafa;
  --primary: #e4e4e7;
  --primary-foreground: #18181b;
  --secondary: #27272a;
  --secondary-foreground: #fafafa;
  --muted: #27272a;
  --muted-foreground: #9f9fa9;
  --accent: #27272a;
  --accent-foreground: #fafafa;
  --destructive: #ff6467;
  --border: #ffffff1a;
  --input: #ffffff26;
  --ring: #71717b;
  --chart-1: #1447e6;
  --chart-2: #00bc7d;
  --chart-3: #fe9a00;
  --chart-4: #ad46ff;
  --chart-5: #ff2056;
  --sidebar: #18181b;
  --sidebar-foreground: #fafafa;
  --sidebar-primary: #1447e6;
  --sidebar-primary-foreground: #fafafa;
  --sidebar-accent: #27272a;
  --sidebar-accent-foreground: #fafafa;
  --sidebar-border: #ffffff1a;
  --sidebar-ring: #71717b;

  --shadow-2xs: 0 1px 3px 0px #000000;
  --shadow-xs: 0 1px 3px 0px #000000;
  --shadow-sm: 0 1px 3px 0px #000000, 0 1px 2px -1px #000000;
  --shadow: 0 1px 3px 0px #000000, 0 1px 2px -1px #000000;
  --shadow-md: 0 1px 3px 0px #000000, 0 2px 4px -1px #000000;
  --shadow-lg: 0 1px 3px 0px #000000, 0 4px 6px -1px #000000;
  --shadow-xl: 0 1px 3px 0px #000000, 0 8px 10px -1px #000000;
  --shadow-2xl: 0 1px 3px 0px #000000;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}


.bg-rotated {
  transform: rotate(180deg) scaleY(-1);
  transform-origin: center;
}

/* Ensure smooth transitions */
.card-bg {
  background-repeat: no-repeat;
  background-size: cover;
  transition: background-image 0.3s ease, transform 0.3s ease;
}

.bg-corner-small {
  background-size: 150px 150px;
}