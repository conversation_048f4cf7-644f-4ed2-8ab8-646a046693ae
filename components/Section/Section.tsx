import * as React from "react";
import { cn } from "@/lib/utils";

interface SectionProps extends React.ComponentProps<"section"> {
  /**
   * Controls the vertical padding of the section
   * @default "default"
   */
  padding?: "none" | "sm" | "default" | "lg" | "xl";
  
  /**
   * Controls the container behavior
   * @default true
   */
  container?: boolean;
  
  /**
   * Controls the horizontal padding when container is true
   * @default true
   */
  containerPadding?: boolean;
  
  /**
   * Background color variant
   * @default "white"
   */
  variant?: "white" | "gray" | "transparent";
  
  /**
   * Whether to add relative positioning for absolute positioned children
   * @default false
   */
  relative?: boolean;
  
  /**
   * Whether to add overflow hidden
   * @default false
   */
  overflow?: boolean;
}

const paddingVariants = {
  none: "",
  sm: "py-8",
  default: "py-16", 
  lg: "py-20",
  xl: "py-24",
};

const backgroundVariants = {
  white: "bg-white",
  gray: "bg-gray-50",
  transparent: "bg-transparent",
};

function Section({
  className,
  padding = "default",
  container = true,
  containerPadding = true,
  variant = "white",
  relative = false,
  overflow = false,
  children,
  ...props
}: SectionProps) {
  const sectionClasses = cn(
    paddingVariants[padding],
    backgroundVariants[variant],
    relative && "relative",
    overflow && "overflow-hidden",
    className
  );

  const containerClasses = cn(
    "mx-auto",
    container && "container",
    containerPadding && "px-4",
    relative && "relative z-10"
  );

  if (container) {
    return (
      <section className={sectionClasses} {...props}>
        <div className={containerClasses}>
          {children}
        </div>
      </section>
    );
  }

  return (
    <section className={sectionClasses} {...props}>
      {children}
    </section>
  );
}

export { Section, type SectionProps };
