"use client";

import Link from "next/link";
import LocaleSwitcher from "../localesSwitcher/localesSwitcher";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { Instagram, Linkedin } from "lucide-react";

export default function Footer() {
  const t = useTranslations("Footer");
  const footerSections = {
    summary: {
      title: t("summaryTitle"),
      links: [
        { title: t("world"), href: "/gt-world" },
        { title: t("privacy"), href: "/gt-privacy" },
      
      ],
    },
    importantLinks: {
      title: t("importantLinksTitle"),
      links: [
        { title: t("services"), href: "/gt-services" },
        { title: t("insights"), href: "/gt-vision" },

      ],
    },
    contactSupport: {
      title: t("contactSupportTitle"),
      links: [
        { title: t("contactUs"), href: "/contact-us" },
       
      ],
    },
    followUs: {
      title: t("followUsTitle"),
      links: [],
    },
  };

  return (
    <footer className="bg-white py-12">
      <div className="container mx-auto px-4">
        {/* Top Section */}
        <div className="flex justify-between items-start mb-16">
          <Image src="/FooterLogo.svg" alt="GEOTECH" width={160} height={60} />
          <LocaleSwitcher />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
          {/* Summary */}
          <div>
            <h3 className="font-bold text-gray-900 mb-6">
              {footerSections.summary.title}
            </h3>
            <ul className="space-y-3">
              {footerSections.summary.links.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-black text-sm font-[400] hover:text-gray-900 transition-colors"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          {/* Important Links */}
          <div>
            <h3 className="font-bold text-gray-900 mb-6">
              {footerSections.importantLinks.title}
            </h3>
            <ul className="space-y-3">
              {footerSections.importantLinks.links.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-black text-sm font-[400] hover:text-gray-900 transition-colors"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          {/* Contact & Support */}
          <div>
            <h3 className="font-bold text-gray-900 mb-6">
              {footerSections.contactSupport.title}
            </h3>
            <ul className="space-y-3">
              {footerSections.contactSupport.links.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-black text-sm font-[400] hover:text-gray-900 transition-colors"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-bold text-gray-900 mb-6">
              {footerSections.followUs.title}
            </h3>
            <div className="flex gap-3">
              {/* Instagram */}
              <Link
                href="#"
                className="w-10 h-10 border-1 border-[#ECECECE5] bg-white rounded-[9px] flex items-center justify-center  hover:bg-gray-50 transition-colors"
              >
                <Instagram />
              </Link>
              {/* X (Twitter) */}
              <Link
                href="#"
                className="w-10 h-10 border-1 border-[#ECECECE5]  bg-white rounded-[9px] flex items-center justify-center  hover:bg-gray-50 transition-colors"
              >
                <svg
                  className="w-6 h-6 text-black text-sm font-[400]"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                </svg>
              </Link>

              {/* LinkedIn */}
              <Link
                href="#"
                className="w-10 h-10 border-1 border-[#ECECECE5] bg-white rounded-[9px] flex items-center justify-center  hover:bg-gray-50 transition-colors"
              >
                <Linkedin />
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="flex justify-between items-center pt-4 border-t border-gray-200">
          {/* Copyright */}
          <div className="text-black text-base font-[600] ">
            {t("copyright", { year: 2025 })}
          </div>

          {/* Vision 2030 Logo */}
          <div className="flex items-center">
            <Image
              src="/Vision2030.svg"
              alt="Vision 2030 Logo"
              width={100}
              height={50}
            />
          </div>
        </div>
      </div>
    </footer>
  );
}
