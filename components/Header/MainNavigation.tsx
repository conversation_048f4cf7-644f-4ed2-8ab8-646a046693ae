"use client";

import Link from "next/link";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";
import { DynamicMegaMenu, NavigationItem } from "./DynamicMegaMenu";
import { useTranslations } from "next-intl";
import { usePathname } from "next/navigation";

interface MainNavigationProps {
  items: NavigationItem[];
  className?: string;
  isHomePage?: boolean;
}

export function MainNavigation({ items, className, isHomePage = true }: MainNavigationProps) {
  const t = useTranslations("Navigation");
  const pathname = usePathname();

  return (
    <NavigationMenu className={className}>
      <NavigationMenuList>
        {items.map((item, index) => {
          const isActive = item.href && pathname === item.href;
          return (
            <NavigationMenuItem key={index}>
              {item.isMegaMenu && item.items ? (
                <>
                  <NavigationMenuTrigger
                    className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-transparent px-4 py-2 text-[16px] font-medium transition-colors focus:outline-none disabled:pointer-events-none disabled:opacity-50",
                      isHomePage
                        ? "text-white hover:bg-white/10 hover:text-white hover:font-bold"
                        : "text-black hover:bg-black/10 hover:text-black hover:font-bold",
                      isActive && "font-extrabold"
                    )}
                  >
                    {t(item.title) || item.title}
                  </NavigationMenuTrigger>
                  <NavigationMenuContent className="border-none shadow-none">
                    <DynamicMegaMenu services={item.items} />
                  </NavigationMenuContent>
                </>
              ) : item.items ? (
                <>
                  <NavigationMenuTrigger
                    className={cn(isActive && "font-extrabold")}
                  >
                    {t(item.title) || item.title}
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                      {item.items.map((subItem, subIndex) => {
                        const isSubActive =
                          subItem.href && pathname === subItem.href;
                        return (
                          <NavigationMenuLink key={subIndex} asChild>
                            <Link
                              href={subItem.href || "#"}
                              className={cn(
                                "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-white/10 hover:text-white focus:bg-blue-50 focus:text-blue-900",
                                isSubActive && "font-extrabold"
                              )}
                            >
                              <div className="text-sm font-medium leading-none">
                                {t(subItem.title) || subItem.title}
                              </div>
                              {subItem.description && (
                                <p className="line-clamp-2 text-sm leading-snug text-gray-600">
                                  {t(subItem.description) ||
                                    subItem.description}
                                </p>
                              )}
                            </Link>
                          </NavigationMenuLink>
                        );
                      })}
                    </div>
                  </NavigationMenuContent>
                </>
              ) : (
                <NavigationMenuLink asChild>
                  <Link
                    href={item.href || "#"}
                    className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-transparent px-4 py-2 text-[16px] font-[400] transition-colors focus:outline-none disabled:pointer-events-none disabled:opacity-50",
                      isHomePage
                        ? "text-white hover:bg-white/10 hover:text-white"
                        : "text-black hover:bg-black/10 hover:text-black",
                      isActive && isHomePage && "font-extrabold !bg-transparent !text-white",
                      isActive && !isHomePage && "font-extrabold !bg-transparent !text-black"
                    )}
                  >
                    {t(item.title) || item.title}
                  </Link>
                </NavigationMenuLink>
              )}
            </NavigationMenuItem>
          );
        })}
      </NavigationMenuList>
    </NavigationMenu>
  );
}
