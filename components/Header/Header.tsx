"use client";
import { ChevronDown, Menu, X } from "lucide-react";
import Image from "next/image";
import LocaleSwitcher from "../localesSwitcher/localesSwitcher";
import { useEffect, useState } from "react";
import Link from "next/link";
import { MainNavigation } from "./MainNavigation";
import { navigationItems } from "@/app/data/navItems";
import { useTranslations } from "next-intl";
import { usePathname } from "next/navigation";

function MobileMenu({ isHomePage }: { isHomePage: boolean }) {
  const t = useTranslations("Navigation");
  const tService = useTranslations("MegaMenu");
  const [isOpen, setIsOpen] = useState(false);
  const [openIndex, setOpenIndex] = useState<number | null>(2);
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }

    // Clean up on unmount
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);
  return (
    <div className="lg:hidden">
      <button onClick={() => setIsOpen(!isOpen)} className=" p-2">
        {isOpen ? (
          <X className={`w-8 h-8 m-2 ${isHomePage ? 'text-white' : 'text-black'}`} />
        ) : (
          <Menu className={`w-8 h-8 m-2 ${isHomePage ? 'text-white' : 'text-black'}`} />
        )}
      </button>

      {isOpen && (
        <div className="absolute h-lvh top-full left-0 right-0 bg-white backdrop-blur-sm border-t border-white/10 py-4 z-50">
          <div className="container justify-between flex flex-col bg-white  mx-auto px-4 space-y-4 h-lvh pb-40">
            <div>
              {navigationItems.map((item, index) => (
                <div key={index}>
                  {item.items ? (
                    <div>
                      <button
                        className={`w-full text-left  font-semibold py-4 flex items-center gap-2 justify-between text-lg ${
                          openIndex !== index ? "border-b border-black/10" : ""
                        }`}
                        onClick={() =>
                          setOpenIndex(openIndex === index ? null : index)
                        }

                      >
                        <span>{t(item.title) || item.title}</span>
                        <span
                          className={`transition-transform ${
                            openIndex === index ? "rotate-180" : ""
                          }`}
                        >
                          <ChevronDown />
                        </span>
                      </button>
                      {openIndex === index && (
                        <div className="pl-4 space-y-2 border-b border-black/10  pb-4">
                          {item.items.map((subItem, subIndex) => (
                            <Link
                              key={subIndex}
                              href={subItem.href || "#"}
                              className="flex transition-colors py-1 items-center gap-2"
                              onClick={() => setIsOpen(false)}
                            >
                              <span>
                                {tService(subItem.title) || subItem.title}
                              </span>
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      href={item.href || "#"}
                      className={`block transition-colors text-lg font-semibold py-4${
                        index !== navigationItems.length - 1
                          ? " border-b border-black/10"
                          : ""
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      {t(item.title) || item.title}
                    </Link>
                  )}
                </div>
              ))}
            </div>

            <div className="border-t flex w-full justify-center items-center border-white/10 ">
              <LocaleSwitcher isHomePage={false} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function Header() {
  const [scrolled, setScrolled] = useState(false);
  const pathname = usePathname();
  const isHomePage = pathname === '/';

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <header
      className={`fixed top-0 w-full px-5 z-50 transition-all
        ${
          isHomePage
            ? `border-b border-white/10 ${
                scrolled ? "bg-black/40 backdrop-blur-md" : "bg-transparent"
              }`
            : "bg-white border-b border-gray-200"
        }
      `}
    >
      <div className="container mx-auto  py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/">
              <Image
                src={`${isHomePage ? "HomePageLogo.svg" : "FooterLogo.svg"}`}
                alt="GEOTECH"
                width={153}
                height={56}
                quality={100}
                unoptimized={true}
              />
            </Link>
          </div>
          <div className="flex-1 flex justify-center">
            <MainNavigation
              items={navigationItems}
              className="hidden lg:flex"
              isHomePage={isHomePage}
            />
          </div>
          <div className="flex items-center lg:hidden ">
            <MobileMenu isHomePage={isHomePage} />
          </div>

          <div className=" items-center hidden lg:flex">
            <LocaleSwitcher isHomePage={isHomePage} />
          </div>
        </div>
      </div>
    </header>
  );
}

export default Header;
