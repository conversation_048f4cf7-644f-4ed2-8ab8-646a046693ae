"use client";

import { useEffect, useState } from "react";
import { ToggleGroup, ToggleGroupItem } from "../ui/toggle-group";

export default function LocaleSwitcher({ isHomePage = true }: { isHomePage?: boolean }) {
  const [currentLocale, setCurrentLocale] = useState("ar");

  useEffect(() => {
    const cookieLocale = document.cookie.match(/(?:^|; )locale=([^;]*)/)?.[1];
    setCurrentLocale(cookieLocale || localStorage.getItem("locale") || "ar");
  }, []);
  const switchLocale = (locale: string) => {
    document.cookie = `locale=${locale}; path=/`;
    location.reload();
  };

  return (
    <ToggleGroup
      type="single"
      value={currentLocale}
      onValueChange={(value) => value && switchLocale(value as "ar" | "en")}
      size="sm"
      className={`!rounded-full p-1 border ${
        isHomePage
          ? 'bg-black/20 backdrop-blur-sm border-white/10'
          : 'bg-black/10 border-gray-300'
      }`}
    >
      <ToggleGroupItem
        value="ar"
        className={`!rounded-full px-3 ml-1 text-sm font-medium transition-all data-[state=on]:bg-white data-[state=on]:text-gray-900 ${
          isHomePage
            ? 'data-[state=off]:text-white/70 data-[state=off]:hover:text-white hover:bg-white/10'
            : 'data-[state=off]:text-gray-600 data-[state=off]:hover:text-black hover:bg-gray-200'
        }`}
      >
        العربية
      </ToggleGroupItem>
      <ToggleGroupItem
        value="en"
        className={`!rounded-full px-3 mr-1 text-sm font-medium transition-all data-[state=on]:bg-white data-[state=on]:text-gray-900 ${
          isHomePage
            ? 'data-[state=off]:text-white/70 data-[state=off]:hover:text-white hover:bg-white/10'
            : 'data-[state=off]:text-gray-600 data-[state=off]:hover:text-black hover:bg-gray-200'
        }`}
      >
        English
      </ToggleGroupItem>
    </ToggleGroup>
  );
}
