import { cn } from "@/lib/utils";

interface SectionHeaderProps {
  badge?: {
    text: string;
    variant?: "default" | "purple" | "green";
    className?: string;
    icon?: React.ReactNode;
  };
  title: string;
  subtitle?: string;
  align?: "left" | "center" | "right";
  titleClassName?: string;
  subtitleClassName?: string;
  className?: string;
}

export function SectionHeader({
  badge,
  title,
  subtitle,
  align = "right",
  titleClassName,
  subtitleClassName,
  className,
}: SectionHeaderProps) {
  const badgeVariants = {
    default: "bg-gray-100 text-gray-800",
    purple: "bg-[#5840BA1A] text-[#5840BA]",
    green: "bg-[#E8F5E9] text-[#2E7D32]",
  };

  return (
    <div
      className={cn(
        "flex flex-col gap-4",
        {
          "items-start": align === "left",
          "items-center": align === "center",
          "items-end": align === "right",
        },
        className
      )}
    >
      {badge && (
        <div
          className={cn(
            "px-2 py-1.5 rounded-full uppercase text-sm font-semibold inline-flex items-center gap-2",
            badgeVariants[badge.variant || "default"],
            badge.className
          )}
        >
          {badge.icon}
          {badge.text}
        </div>
      )}
      <h2
        className={cn(
          "text-[32px] font-bold leading-tight text-gray-900",
          {
            "text-left": align === "left",
            "text-center": align === "center",
            "text-right": align === "right",
          },
          titleClassName
        )}
      >
        {title}
      </h2>
      {subtitle && (
        <p
          className={cn(
            "text-base text-gray-600",
            {
              "text-left": align === "left",
              "text-center": align === "center",
              "text-right": align === "right",
            },
            subtitleClassName
          )}
        >
          {subtitle}
        </p>
      )}
    </div>
  );
}
