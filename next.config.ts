import createNextIntlPlugin from "next-intl/plugin";
import type { NextConfig } from "next";

const withNextIntl = createNextIntlPlugin();

const nextConfig: NextConfig = {
  webpack(config) {
    // Exclude SVG from Next.js default file/image loader
    const assetRule = config.module.rules.find(
      (rule: {
        test?: { toString?: () => string };
        type?: string;
        exclude?: RegExp;
      }) =>
        rule?.test?.toString?.().includes("svg") &&
        rule?.type === "asset/resource"
    );
    if (assetRule) {
      assetRule.exclude = /\.svg$/i;
    }

    // Add SVGR loader
    config.module.rules.push({
      test: /\.svg$/i,
      issuer: /\.[jt]sx?$/,
      use: [
        {
          loader: "@svgr/webpack",
          options: {
            icon: true,
            svgo: true,
            svgoConfig: {
              plugins: [
                {
                  name: "removeViewBox",
                  active: false,
                },
                {
                  name: "removeDimensions",
                  active: false,
                },
              ],
            },
          },
        },
      ],
    });

    return config;
  },
};

export default withNextIntl(nextConfig);
